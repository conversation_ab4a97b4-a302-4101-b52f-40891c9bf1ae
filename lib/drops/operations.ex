defmodule Drops.Operations do
  @moduledoc """
  Operations module for defining command and query operations.

  This module provides a framework for defining operations that can be used
  to encapsulate business logic with input validation and execution.
  """

  defmodule Success do
    @type t :: %__MODULE__{}

    defstruct [:operation, :result, :params, :type]
  end

  defmodule Failure do
    @type t :: %__MODULE__{}

    defstruct [:operation, :result, :params, :type]
  end

  @doc """
  Callback for executing an operation with given parameters.
  """
  @callback perform(params :: any()) :: {:ok, any()} | {:error, any()}

  @callback validate(params :: any()) :: {:ok, any()} | {:error, any()}

  defmacro __using__(_opts) do
    quote do
      import Drops.Operations

      defmacro __using__(type) when is_atom(type) do
        Drops.Operations.__define_operation__(type: type)
      end

      defmacro __using__(opts) when is_list(opts) do
        unless Keyword.has_key?(opts, :type) do
          raise ArgumentError, "type option is required when using Drops.Operations"
        end

        Drops.Operations.__define_operation__(opts)
      end
    end
  end

  @doc false
  def __define_operation__(opts) do
    quote do
      @behaviour Drops.Operations

      use Drops.Contract

      # Store the repo configuration if provided
      @repo unquote(opts[:repo])

      # Store the operation type
      @operation_type unquote(opts[:type])

      schema do
        %{}
      end

      def execute(params) do
        case validate(params) do
          {:ok, params} ->
            case perform(params) do
              {:ok, result} ->
                {:ok,
                 %Drops.Operations.Success{
                   operation: __MODULE__,
                   result: result,
                   params: params,
                   type: @operation_type
                 }}

              {:error, error} ->
                {:error,
                 %Drops.Operations.Failure{
                   operation: __MODULE__,
                   result: error,
                   params: params,
                   type: @operation_type
                 }}
            end

          {:error, errors} ->
            {:error,
             %Drops.Operations.Failure{
               operation: __MODULE__,
               result: errors,
               params: params,
               type: @operation_type
             }}
        end
      end

      def perform(params) do
        raise "perform/1 must be implemented"
      end

      def validate(params) do
        if length(schema().keys) == 0, do: {:ok, params}, else: conform(params)
      end

      def changeset(params) do
        source_schema = schema().meta[:source_schema]

        Ecto.Changeset.change(struct(source_schema), params)
      end

      if unquote(opts[:repo]) do
        def persist(params) do
          @repo.insert(changeset(params))
        end
      end

      defoverridable perform: 1
      defoverridable validate: 1
    end
  end
end
